#!/usr/bin/env node

/**
 * Test script to verify the new stream configuration formats
 * work correctly with the refactored IQ stream creation architecture
 */

// Load the BladeRF addon
let addon;
try {
    try {
        addon = require('../build/Debug/bladerf_addon.node');
        console.log('✅ BladeRF addon loaded successfully (Debug)');
    } catch (debugError) {
        addon = require('../build/Release/bladerf_addon.node');
        console.log('✅ BladeRF addon loaded successfully (Release)');
    }
} catch (error) {
    console.error('❌ Failed to load BladeRF addon:', error.message);
    process.exit(1);
}

console.log('\n🧪 Testing Stream Configuration Formats');
console.log('🏗️  Using refactored IQ stream creation architecture\n');

// Test WAV configuration
console.log('1️⃣  Testing WAV Stream Configuration:');
const wavConfig = {
    onFrame: (frameData) => {
        console.log(`   🎬 WAV Frame received: ${frameData.frameNumber}`);
        processor1.stop();
    },
    onError: (code, message) => {
        console.log(`   ❌ WAV Error: ${code} - ${message}`);
    },
    onStop: (code) => {
        console.log(`   🛑 WAV Stopped: ${code}`);
        testBladeRFConfig();
    },
    onEvent: (eventName, payload) => {
        console.log(`   📡 WAV Event: ${eventName}`);
        if (eventName === 'streamReady') {
            console.log(`      ✅ Stream Type: ${payload.type}`);
            console.log(`      📊 Sample Rate: ${payload.sampleRateHz} Hz`);
            console.log(`      📁 Source: ${payload.sourceName}`);
        }
    },
    stream: {
        source: 'wav',
        wav: {
            path: 'samples/recording.wav',
            loop: true,
            simulateTiming: true
        }
    }
};

let processor1, processor2;

try {
    processor1 = addon.createIQVideoProcessor(wavConfig);
    console.log('   ✅ WAV processor created successfully');
} catch (error) {
    console.log(`   ❌ WAV processor failed: ${error.message}`);
    testBladeRFConfig();
}

function testBladeRFConfig() {
    console.log('\n2️⃣  Testing BladeRF Stream Configuration:');
    
    const bladeRFConfig = {
        onFrame: (frameData) => {
            console.log(`   🎬 BladeRF Frame received: ${frameData.frameNumber}`);
            processor2.stop();
        },
        onError: (code, message) => {
            console.log(`   ❌ BladeRF Error: ${code} - ${message}`);
        },
        onStop: (code) => {
            console.log(`   🛑 BladeRF Stopped: ${code}`);
            console.log('\n✅ Configuration tests completed!');
            process.exit(0);
        },
        onEvent: (eventName, payload) => {
            console.log(`   📡 BladeRF Event: ${eventName}`);
            if (eventName === 'streamReady') {
                console.log(`      ✅ Stream Type: ${payload.type}`);
                console.log(`      📻 Frequency: ${(payload.frequencyHz / 1e9).toFixed(2)} GHz`);
                console.log(`      📊 Sample Rate: ${(payload.sampleRateHz / 1e6).toFixed(1)} MHz`);
                console.log(`      📶 Bandwidth: ${(payload.bandwidthHz / 1e6).toFixed(1)} MHz`);
                console.log(`      🎚️  Gain: ${payload.gainMode} (${payload.manualGainDb || 'auto'} dB)`);
                console.log(`      📡 Channel: ${payload.channel}`);
            }
        },
        stream: {
            source: 'bladerf',
            bladerf: {
                frequencyHz: 5800000000,    // 5.8 GHz
                sampleRateHz: 20000000,     // 20 MHz
                bandwidthHz: 20000000,      // 20 MHz
                channel: 0,
                gainMode: 'manual',
                manualGainDb: 30
            }
        }
    };

    try {
        processor2 = addon.createIQVideoProcessor(bladeRFConfig);
        console.log('   ✅ BladeRF processor created successfully');
    } catch (error) {
        console.log(`   ❌ BladeRF processor failed: ${error.message}`);
        console.log('      (This is expected if no BladeRF hardware is connected)');
        console.log('\n✅ Configuration tests completed!');
        process.exit(0);
    }
}

// Handle cleanup
process.on('SIGINT', () => {
    console.log('\n🛑 Cleaning up...');
    if (processor1) processor1.stop();
    if (processor2) processor2.stop();
    process.exit(0);
});

setTimeout(() => {
    console.log('\n⏰ Test timeout - stopping processors');
    if (processor1) processor1.stop();
    if (processor2) processor2.stop();
}, 10000);
