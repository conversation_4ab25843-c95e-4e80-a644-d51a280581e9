#include "bladerf_stream.h"
#include "logging/logging.h"
#include <algorithm>
#include <cmath>

static constexpr double MIN_TIMEOUT_MS = 20.0;
static constexpr double MAX_TIMEOUT_MS = 5000.0;
static constexpr size_t MAX_CONSECUTIVE_TIMEOUTS = 2;

BladeRFIQStream::BladeRFIQStream(const Config& cfg, const BufferConfig& bufferCfg) : cfg_(cfg), bufferCfg_(bufferCfg) {}

BladeRFIQStream::~BladeRFIQStream() {
  BladeRFIQStream::close();
}

bool BladeRFIQStream::open() {
  LOG_VERBOSE(BLADERF_STREAM, "open() entry");

  if (isOpen_) {
    LOG_VERBOSE(BLADERF_STREAM, "stream already open, closing first");
    close();
  }

  // Keep SDK logs reasonable
  bladerf_log_set_verbosity(BLADERF_LOG_LEVEL_WARNING);
  LOG_VERBOSE(BLADERF_STREAM, "set BladeRF SDK log verbosity to WARNING");

  // Open first available device
  LOG_VERBOSE(BLADERF_STREAM, "attempting to open BladeRF device");
  int st = bladerf_open(&dev_, nullptr);
  if (st != 0) {
    return setError("failed to open BladeRF device: " + std::string(bladerf_strerror(st)));
  }
  LOG_VERBOSE(BLADERF_STREAM, "BladeRF device opened successfully");

  if (!configureDevice()) {
    LOG_ERROR(BLADERF_STREAM, "device configuration failed");
    bladerf_close(dev_);
    dev_ = nullptr;
    return false;
  }

  if (!configureSyncRx()) {
    LOG_ERROR(BLADERF_STREAM, "sync RX configuration failed");
    bladerf_close(dev_);
    dev_ = nullptr;
    return false;
  }

  // Enable RX module
  LOG_VERBOSE(BLADERF_STREAM, "enabling RX module on channel " << cfg_.channel);
  st = bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(cfg_.channel), true);
  if (st != 0) {
    setError("failed to enable RX module: " + std::string(bladerf_strerror(st)));
    bladerf_close(dev_);
    dev_ = nullptr;
    return false;
  }

  isOpen_ = true;
  active_ = true;

  LOG_VERBOSE(BLADERF_STREAM, "BladeRF stream opened successfully - freq=" << info_.frequencyHz
        << "Hz, rate=" << info_.sampleRateHz << "Hz, bw=" << info_.bandwidthHz
        << "Hz, ch=" << info_.channel << ", gainMode=" << static_cast<int>(info_.gainMode));
  if (info_.gainMode == GainMode::MANUAL) {
    LOG_VERBOSE(BLADERF_STREAM, "manual gain set to " << info_.manualGainDb << "dB");
  }

  return true;
}

bool BladeRFIQStream::configureDevice() {
  LOG_VERBOSE(BLADERF_STREAM, "configureDevice() entry");

  if (!dev_) {
    return setError("configureDevice failed: device not open");
  }

  // Frequency
  {
    LOG_VERBOSE(BLADERF_STREAM, "setting frequency to " << cfg_.frequencyHz << "Hz");
    auto st = bladerf_set_frequency(dev_, BLADERF_CHANNEL_RX(cfg_.channel), static_cast<uint64_t>(cfg_.frequencyHz));
    if (st != 0) {
      return setError("failed to set frequency: " + std::string(bladerf_strerror(st)));
    }
    uint64_t actual = 0;
    st = bladerf_get_frequency(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actual);
    if (st == 0) {
      info_.frequencyHz = static_cast<double>(actual);
      LOG_VERBOSE(BLADERF_STREAM, "frequency set successfully, actual: " << actual << "Hz");
    } else {
      return setError("failed to get frequency: " + std::string(bladerf_strerror(st)));
    }
  }

  // Sample rate
  {
    LOG_VERBOSE(BLADERF_STREAM, "setting sample rate to " << cfg_.sampleRateHz << "Hz");
    uint32_t actual = 0;
    const auto st = bladerf_set_sample_rate(dev_, BLADERF_CHANNEL_RX(cfg_.channel), static_cast<uint32_t>(cfg_.sampleRateHz), &actual);
    if (st != 0) {
      return setError("failed to set sample rate: " + std::string(bladerf_strerror(st)));
    }
    info_.sampleRateHz = static_cast<double>(actual);
    LOG_VERBOSE(BLADERF_STREAM, "sample rate set successfully, actual: " << actual << "Hz");
  }

  // Bandwidth
  {
    LOG_VERBOSE(BLADERF_STREAM, "setting bandwidth to " << cfg_.bandwidthHz << "Hz");
    uint32_t actual = 0;
    const auto st = bladerf_set_bandwidth(dev_, BLADERF_CHANNEL_RX(cfg_.channel), static_cast<uint32_t>(cfg_.bandwidthHz), &actual);
    if (st != 0) {
      return setError("failed to set bandwidth: " + std::string(bladerf_strerror(st)));
    }
    info_.bandwidthHz = static_cast<double>(actual);
    LOG_VERBOSE(BLADERF_STREAM, "bandwidth set successfully, actual: " << actual << "Hz");
  }

  // Gain mode / Manual gain
  {
    LOG_VERBOSE(BLADERF_STREAM, "setting gain mode to " << static_cast<int>(cfg_.gainMode));
    const bladerf_gain_mode mode = mapGainMode(cfg_.gainMode);
    auto st = bladerf_set_gain_mode(dev_, BLADERF_CHANNEL_RX(cfg_.channel), mode);
    if (st != 0) {
      return setError("failed to set gain mode: " + std::string(bladerf_strerror(st)));
    }

    // Read back actual mode (defensive)
    bladerf_gain_mode actualMode = BLADERF_GAIN_DEFAULT;
    st = bladerf_get_gain_mode(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actualMode);
    if (st == 0) {
      // Map back
      switch (actualMode) {
        case BLADERF_GAIN_MANUAL:  info_.gainMode = GainMode::MANUAL;  break;
        case BLADERF_GAIN_FASTATTACK_AGC: info_.gainMode = GainMode::FAST; break;
        case BLADERF_GAIN_SLOWATTACK_AGC: info_.gainMode = GainMode::SLOW; break;
        default: info_.gainMode = GainMode::DEFAULT_; break;
      }
      LOG_VERBOSE(BLADERF_STREAM, "gain mode set successfully, actual mode: " << static_cast<int>(info_.gainMode));
    } else {
      // If unable to read, assume configured
      info_.gainMode = cfg_.gainMode;
      LOG_WARNING(BLADERF_STREAM, "could not read back gain mode, assuming configured value");
    }

    if (info_.gainMode == GainMode::MANUAL) {
      LOG_VERBOSE(BLADERF_STREAM, "setting manual gain to " << cfg_.manualGainDb << "dB");
      st = bladerf_set_gain(dev_, BLADERF_CHANNEL_RX(cfg_.channel), cfg_.manualGainDb);
      if (st != 0) {
        return setError("failed to manual gain: " + std::string(bladerf_strerror(st)));
      }
      int actualGain = 0;
      st = bladerf_get_gain(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actualGain);
      if (st == 0) {
        info_.manualGainDb = actualGain;
        LOG_VERBOSE(BLADERF_STREAM, "manual gain set successfully, actual: " << actualGain << "dB");
      } else {
        info_.manualGainDb = cfg_.manualGainDb;
        LOG_WARNING(BLADERF_STREAM, "could not read back manual gain, assuming configured value");
      }
    } else {
      info_.manualGainDb = 0;
    }
  }

  info_.channel = cfg_.channel;
  LOG_VERBOSE(BLADERF_STREAM, "device configuration completed successfully");

  return true;
}

bool BladeRFIQStream::configureSyncRx() {
  LOG_VERBOSE(BLADERF_STREAM, "configureSyncRx() entry");

  if (!dev_) {
    return setError("configureSyncRx failed: device not open");
  }

  LOG_VERBOSE(BLADERF_STREAM, "configuring sync RX with " << bufferCfg_.num_buffers << " buffers, " << bufferCfg_.buffer_size << " samples per buffer, " << bufferCfg_.num_transfers << " transfers");
  const auto st = bladerf_sync_config(
    dev_,
    BLADERF_RX_X1,
    BLADERF_FORMAT_SC16_Q11,
    bufferCfg_.num_buffers,
    bufferCfg_.buffer_size,
    bufferCfg_.num_transfers,
    /*stream_timeout_ms*/ 0  // TODO set the per-call timeout here instead of readSamples()
  );
  if (st != 0) {
    return setError("failed to configure sync RX: " + std::string(bladerf_strerror(st)));
  }

  LOG_VERBOSE(BLADERF_STREAM, "sync RX configuration completed successfully");
  return true;
}

bool BladeRFIQStream::readSamples(SampleType* dst, const size_t sampleCount) {
  if (!isOpen_ || !active_) {
    LOG_ERROR(BLADERF_STREAM, "readSamples failed: stream not open or not active");
    return false;
  }
  if (!dst) {
    return setError("readSamples failed: destination buffer is null");
  }
  if (sampleCount == 0) {
    LOG_VERBOSE(BLADERF_STREAM, "readSamples: zero samples requested, returning success");
    return true;
  }

  // Compute dynamic timeout: 2x expected time to read all samples
  const auto expectedMs = (info_.sampleRateHz > 0.0) ? (1000.0 * static_cast<double>(sampleCount) / info_.sampleRateHz) : 1000.0; // fallback
  const auto timeoutMs = static_cast<unsigned int>(
    std::clamp(expectedMs * 2.0, MIN_TIMEOUT_MS, MAX_TIMEOUT_MS)
  );

  size_t timeouts = 0;
  while (true) {
    // Direct BladeRF API call to destination buffer (cast to void* for compatibility)
    const auto st = bladerf_sync_rx(dev_, dst, sampleCount, nullptr, timeoutMs);
    if (st == 0) return true;

    if (st == BLADERF_ERR_TIMEOUT) {
      LOG_WARNING(BLADERF_STREAM, "bladerf_sync_rx timeout (attempt " << (timeouts + 1) << "/" << (MAX_CONSECUTIVE_TIMEOUTS + 1) << ")");
      if (++timeouts > MAX_CONSECUTIVE_TIMEOUTS) {
        active_ = false;
        return setError("RX timeout after " + std::to_string(MAX_CONSECUTIVE_TIMEOUTS) + " retries");
      }
      // -> CONTINUE
    } else {
      active_ = false;
      return setError("bladerf_sync_rx failed: " + std::string(bladerf_strerror(st)));
    }
  }
}

void BladeRFIQStream::close() noexcept {
  LOG_VERBOSE(BLADERF_STREAM, "close() entry");

  active_ = false;

  if (dev_) {
    LOG_VERBOSE(BLADERF_STREAM, "disabling RX module and closing device");

    // Best-effort disable RX; if it fails, continue
    const auto st = bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(cfg_.channel), false);
    if (st != 0) {
      LOG_ERROR(BLADERF_STREAM, "disable RX failed during close: " << bladerf_strerror(st));
    } else {
      LOG_VERBOSE(BLADERF_STREAM, "RX module disabled successfully");
    }

    bladerf_close(dev_);
    dev_ = nullptr;
    LOG_VERBOSE(BLADERF_STREAM, "BladeRF device closed");
  }

  isOpen_ = false;
  LOG_VERBOSE(BLADERF_STREAM, "close() completed");
}

SampleRateType BladeRFIQStream::sampleRate() const noexcept {
  // IIQStream requires uint32_t; use actual
  const auto sr = info_.sampleRateHz > 0.0 ? info_.sampleRateHz : cfg_.sampleRateHz;
  const auto clamped = std::min<uint64_t>(static_cast<uint64_t>(std::llround(sr)), 0xFFFFFFFFull);
  return static_cast<SampleRateType>(clamped);
}

const std::string& BladeRFIQStream::sourceName() const noexcept {
  return sourceName_;
}

bool BladeRFIQStream::isActive() const noexcept {
  return isOpen_ && active_;
}

const std::string& BladeRFIQStream::lastError() const noexcept {
  return lastError_;
}

BladeRFIQStream::ResolvedInfo BladeRFIQStream::getResolvedInfo() const noexcept {
  return info_;
}

bool BladeRFIQStream::setError(const std::string& err) {
  LOG_ERROR(BLADERF_STREAM, "error set: " << err);
  lastError_ = err;
  return false;
}

bladerf_gain_mode BladeRFIQStream::mapGainMode(GainMode m) {
  switch (m) {
    case GainMode::MANUAL:   return BLADERF_GAIN_MANUAL;
    case GainMode::FAST:   return BLADERF_GAIN_FASTATTACK_AGC;
    case GainMode::SLOW:   return BLADERF_GAIN_SLOWATTACK_AGC;
    case GainMode::DEFAULT_: return BLADERF_GAIN_DEFAULT;
  }
  return BLADERF_GAIN_DEFAULT;
}