#include <node.h>
#include <node_buffer.h>
#include <iostream>
#include "video_processor_wrapper.h"
#include "video-processor/video_processor_configs.h"
#include "wav-stream/wav_stream.h"
#include "bladerf-stream/bladerf_stream.h"
#include "node/v8_helpers.h"
#include "logging/logging.h"

namespace VideoDecodingAddon {

/**
 * NODE SPECIFIC IMPLEMENTATION AND METHODS
 */
v8::Persistent<v8::Function> VideoProcessorWrapper::constructor;

void VideoProcessorWrapper::Node_Init(const v8::Local<v8::Object> exports) {
  v8::Isolate* isolate = exports->GetIsolate();

  // Prepare constructor template
  const v8::Local<v8::FunctionTemplate> tpl = v8::FunctionTemplate::New(isolate, Node_New);
  tpl->SetClassName(v8::String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked());
  tpl->InstanceTemplate()->SetInternalFieldCount(1);

  // Prototype methods
  NODE_SET_PROTOTYPE_METHOD(tpl, "stop", Stop_Prototype);

  constructor.Reset(isolate, tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked());
  exports->Set(
    isolate->GetCurrentContext(),
    v8::String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked(),
    tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked()
  ).Check();

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "js prototype initialized");
}

void VideoProcessorWrapper::Node_New(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Isolate* isolate = args.GetIsolate();

  if (args.IsConstructCall()) {
    // Invoked as constructor: `new VideoProcessor(...)`
    auto* obj = new VideoProcessorWrapper();
    obj->Wrap(args.This());
    args.GetReturnValue().Set(args.This());
  } else {
    // Invoked as plain function `VideoProcessor(...)`, turn into construct call.
    constexpr int argc = 0;
    v8::Local<v8::Value> argv[1] = {};
    const v8::Local<v8::Function> cons = v8::Local<v8::Function>::New(isolate, constructor);
    const v8::Local<v8::Context> context = isolate->GetCurrentContext();
    const v8::Local<v8::Object> result = cons->NewInstance(context, argc, argv).ToLocalChecked();
    args.GetReturnValue().Set(result);
  }
}

void VideoProcessorWrapper::Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Isolate* isolate = args.GetIsolate();
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "received stop request from js");
  auto* videoProcessor = Unwrap<VideoProcessorWrapper>(args.Holder());
  videoProcessor->stop();
  args.GetReturnValue().Set(v8::Boolean::New(isolate, true));
}

VideoProcessorWrapper::~VideoProcessorWrapper() {
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "destroying");
  try {
    stop(); // idempotent; best-effort; JS callbacks may not run during isolate teardown
  } catch (...) {
    // swallow
  }
}

bool VideoProcessorWrapper::start(const v8::Local<v8::Object> configObject) {
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "starting");
  if (!initializeNodeCallbacks(configObject)) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to initialize node callbacks");
    return false;
  }

  auto iqStream = createIQStream(configObject);
  if (!iqStream) {
    // emit error (we are on Node thread here)
    emitErrorToJS(ErrorCode::STREAM_OPEN_FAILED, "failed to create IQStream");
    return false;
  }

  // Create VideoProcessor instance
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "creating video processor instance");
  videoProcessor_ = std::make_unique<IQVideoProcessor::VideoProcessor>(
    std::move(iqStream),
    // onFrameAvailable: signal frames-to-emit on Node thread
    [this]() {
      if (!nodeRunner_ || !videoProcessor_) return;
      nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) {
        this->emitFramesToJS();
      });
    },
    // onStopped: runs on joiner thread after full teardown
    [this](StopCode code) {
      if (!nodeRunner_ || !videoProcessor_) return;
      nodeRunner_->run([this, code](v8::Isolate*, v8::Local<v8::Context>) {
        // If pipeline exited or internal error, emit onError before onStop
        if (!errorEmitted_.load(std::memory_order_acquire)) {
          if (code == StopCode::PIPELINE_EXIT) {
            emitErrorToJS(ErrorCode::PIPELINE_EXIT, "pipeline exited unexpectedly");
          } else if (code == StopCode::ERROR) {
            emitErrorToJS(ErrorCode::INTERNAL_ERROR, "internal error caused stop");
          }
        }
        this->finalizeStopOnNodeThread(stopCodeToInt(code));
      });
    }
  );

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "triggering start on video processor");
  if (!videoProcessor_->start()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to start video processor");
    // emit error (Node thread)
    emitErrorToJS(ErrorCode::START_FAILED, "videoProcessor.start() failed");
    videoProcessor_.reset();
    return false;
  }

  return true;
}

void VideoProcessorWrapper::stop() {
  if (bool expected = false; !stopping_.compare_exchange_strong(expected, true)) {
    return; // already stopping/stopped
  }

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "stop command received");
  if (videoProcessor_) {
    videoProcessor_->stop(StopCode::USER_REQUEST);
  } else {
    // No processor: finalize directly on Node thread with user request code
    if (nodeRunner_) {
      const auto userStopCode = stopCodeToInt(StopCode::USER_REQUEST);
      nodeRunner_->run([this, userStopCode](v8::Isolate*, v8::Local<v8::Context>) {
        this->finalizeStopOnNodeThread(userStopCode);
      });
    }
  }
}

void VideoProcessorWrapper::finalizeStopOnNodeThread(const int code) {
  if (stopEmitted_.exchange(true)) {
    return; // already finalized
  }

  if (videoProcessor_) {
    videoProcessor_.reset();
  }

  emitStopToJS(code);

  // Clear callbacks after emission
  onStopCallback_.Reset();
  onErrorCallback_.Reset();
  onFrameCallback_.Reset();
  onEventCallback_.Reset();

  // Disable Node runner last
  if (nodeRunner_) {
    nodeRunner_->disable();
    nodeRunner_.reset();
  }
}

void VideoProcessorWrapper::emitFramesToJS() const {
  if (!videoProcessor_ || onFrameCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onFrameCallback_);

  while (videoProcessor_->hasNextFrame()) {
    const auto& frame = videoProcessor_->getNextFrame(); // Consume the frame

    // Prepare JS object with a Node.js Buffer (copy)
    v8::Local<v8::Object> bufferObj;
    {
      const char* dataPtr = reinterpret_cast<const char*>(frame.data.data());
      const size_t dataSize = frame.dataSize;
      bufferObj = node::Buffer::Copy(isolate, dataPtr, dataSize).ToLocalChecked();
      // NOTE: Zero-copy would require the pipeline to allocate frames in
      // a ref-counted buffer and handing ownership to Node via a finalizer.
    }

    const v8::Local<v8::Object> frameObj = v8::Object::New(isolate);
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "image").ToLocalChecked(), bufferObj).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "frameNumber").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.frameNumber))).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "width").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.width))).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "height").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.height))).Check();

    LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "emitting frame " << frame.frameNumber << " to JS");
    v8::Local<v8::Value> argv[1] = { frameObj };
    if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to call JavaScript frame callback");
    }
  }
}

void VideoProcessorWrapper::emitStopToJS(const int code) const {
  if (onStopCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onStopCallback_);
  v8::Local<v8::Value> argv[1] = { v8::Number::New(isolate, static_cast<double>(code)) };
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "emitting stop event to JS");
  if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to call JavaScript onStop callback");
  }
}

void VideoProcessorWrapper::emitErrorToJS(const ErrorCode code, const std::string& message) const {
  if (onErrorCallback_.IsEmpty()) return;

  // Ensure we only emit the first error (aligns with "errors imply stop")
  if (bool expected = false; !const_cast<std::atomic<bool>&>(errorEmitted_).compare_exchange_strong(expected, true)) {
    return;
  }

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onErrorCallback_);

  v8::Local<v8::Value> argv[2] = {
    v8::Number::New(isolate, errorCodeToInt(code)),
    v8::String::NewFromUtf8(isolate, message.c_str()).ToLocalChecked()
  };

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "emitting error to JS; code=" << errorCodeToInt(code) << "; msg=" << message);
  if (callbackFunction->Call(context, v8::Null(isolate), 2, argv).IsEmpty()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to call JavaScript onError callback");
  }
}

void VideoProcessorWrapper::emitEventToJS(const char* eventName, const v8::Local<v8::Object> payload) const {
  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> cb = v8::Local<v8::Function>::New(isolate, onEventCallback_);

  v8::Local<v8::Value> argv[2] = {
    v8::String::NewFromUtf8(isolate, eventName).ToLocalChecked(),
    payload
  };
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "emitting event to JS: " << eventName);
  if (cb->Call(context, v8::Null(isolate), 2, argv).IsEmpty()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to call JavaScript onEvent callback");
  }
}

/**
 * Initialize Node.js callbacks from the provided configuration object
 * Expects the configObject to have the following properties:
 * - onEvent: function to call on events (e.g., "ready")
 * - onStop: function to call when processing stops
 * - onError: function to call on errors
 * - onFrame: function to call when a new frame is available
 * Returns true if all callbacks are successfully initialized, false otherwise
 */

bool VideoProcessorWrapper::initializeNodeCallbacks(const v8::Local<v8::Object>& configObject) {
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "initializing node callbacks");
  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();

  const auto onStopKey  = v8::String::NewFromUtf8(isolate, "onStop").ToLocalChecked();
  const auto onErrorKey = v8::String::NewFromUtf8(isolate, "onError").ToLocalChecked();
  const auto onFrameKey = v8::String::NewFromUtf8(isolate, "onFrame").ToLocalChecked();
  const auto onEventKey = v8::String::NewFromUtf8(isolate, "onEvent").ToLocalChecked();

  v8::Local<v8::Value> onStopVal, onErrorVal, onFrameVal, onEventVal;
  if (!configObject->Get(context, onStopKey).ToLocal(&onStopVal) ||
      !configObject->Get(context, onErrorKey).ToLocal(&onErrorVal) ||
      !configObject->Get(context, onFrameKey).ToLocal(&onFrameVal) ||
      !configObject->Get(context, onEventKey).ToLocal(&onEventVal)) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to get one or more callback properties from configObject");
    return false;
  }

  if (!onStopVal->IsFunction() || !onErrorVal->IsFunction() || !onFrameVal->IsFunction() || !onEventVal->IsFunction()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "one or more callback properties is not a function");
    return false;
  }

  onStopCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onStopVal));
  onErrorCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onErrorVal));
  onFrameCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onFrameVal));
  onEventCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onEventVal));

  // NodeThreadRunner binds to the default loop for the main thread.
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "initializing NodeThreadRunner");
  nodeRunner_ = std::make_unique<NodeHelpers::NodeThreadRunner>(isolate);
  if (!nodeRunner_->enable()) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "failed to enable NodeThreadRunner");
    // We are on Node thread, can emit error directly
    emitErrorToJS(ErrorCode::NODE_RUNNER_ENABLE_FAILED, "failed to initialize NodeThreadRunner");
    return false;
  }
  return true;
}

std::unique_ptr<IIQStream> VideoProcessorWrapper::createIQStream(const v8::Local<v8::Object>& configObject) const {
  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  const v8::Local<v8::Context> ctx = isolate->GetCurrentContext();

  const auto streamKey = v8::String::NewFromUtf8(isolate, "stream").ToLocalChecked();
  v8::Local<v8::Value> streamVal;
  if (!configObject->Get(ctx, streamKey).ToLocal(&streamVal) || !streamVal->IsObject()) {
    LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "no stream config provided; defaulting to WAV sample");
    auto wav = std::make_unique<WAVIQStream>("samples/recording.wav", /*loop*/true, /*timing*/true);
    if (!wav->open()) {
      LOG_ERROR(WAV_STREAM, "failed to open WAV file: " << wav->lastError());
      return nullptr;
    }
    return wav;
  }
  auto streamObj = v8::Local<v8::Object>::Cast(streamVal);

  std::string source;
  if (!NodeHelpers::GetString(isolate, streamObj, "source", source)) {
    LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "stream.source is required and must be a string");
    return nullptr;
  }

  if (source == "wav") {
    // Nested wav group
    const auto wavKey = v8::String::NewFromUtf8(isolate, "wav").ToLocalChecked();
    v8::Local<v8::Value> wavVal;
    if (!streamObj->Get(ctx, wavKey).ToLocal(&wavVal) || !wavVal->IsObject()) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "stream.wav object required when source=='wav'");
      return nullptr;
    }
    auto wavObj = v8::Local<v8::Object>::Cast(wavVal);

    std::string path;
    if (!NodeHelpers::GetString(isolate, wavObj, "path", path) || path.empty()) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "stream.wav.path is required");
      return nullptr;
    }
    bool loop = true;
    bool timing = true;
    if (!NodeHelpers::GetBool(isolate, wavObj, "loop", true, loop) ||
        !NodeHelpers::GetBool(isolate, wavObj, "simulateTiming", true, timing)) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "invalid wav options");
      return nullptr;
    }

    LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "creating WAV IQStream; path=" << path << ", loop=" << loop << ", timing=" << timing);
    auto wav = std::make_unique<WAVIQStream>(path, loop, timing);
    if (!wav->open()) {
      LOG_ERROR(WAV_STREAM, "failed to open WAV file: " << wav->lastError());
      return nullptr;
    }

    // Emit streamReady event for WAV stream
    v8::Local<v8::Object> payload = v8::Object::New(isolate);
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "type").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "wav").ToLocalChecked()).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sourceName").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "wav").ToLocalChecked()).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sampleRateHz").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(wav->sampleRate()))).Check();
    emitEventToJS("streamReady", payload);

    return wav;
  }

  if (source == "bladerf") {
    // Nested bladerf group
    const auto brfKey = v8::String::NewFromUtf8(isolate, "bladerf").ToLocalChecked();
    v8::Local<v8::Value> brfVal;
    if (!streamObj->Get(ctx, brfKey).ToLocal(&brfVal) || !brfVal->IsObject()) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "stream.bladerf object required when source=='bladerf'");
      return nullptr;
    }
    auto brfObj = v8::Local<v8::Object>::Cast(brfVal);

    double freq = 915e6, rate = 1.0e6, bw = 1.5e6;
    int ch = 0, gainDb = 30;
    std::string gainModeStr = "manual";

    if (!NodeHelpers::GetNumber(isolate, brfObj, "frequencyHz", freq, freq) ||
        !NodeHelpers::GetNumber(isolate, brfObj, "sampleRateHz", rate, rate) ||
        !NodeHelpers::GetNumber(isolate, brfObj, "bandwidthHz", bw, bw) ||
        !NodeHelpers::GetInt(isolate, brfObj, "channel", ch, ch)) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "invalid bladerf numeric options");
      return nullptr;
    }
    {
      v8::Local<v8::Value> gmVal;
      if (brfObj->Get(ctx, v8::String::NewFromUtf8(isolate, "gainMode").ToLocalChecked()).ToLocal(&gmVal) && gmVal->IsString()) {
        v8::String::Utf8Value s(isolate, gmVal);
        gainModeStr = *s ? *s : "manual";
      }
    }
    if (!NodeHelpers::GetInt(isolate, brfObj, "manualGainDb", 30, gainDb)) {
      LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "invalid bladerf.manualGainDb");
      return nullptr;
    }

    auto gm = BladeRFIQStream::GainMode::MANUAL;
    if (gainModeStr == "fast") gm = BladeRFIQStream::GainMode::FAST;
    else if (gainModeStr == "slow") gm = BladeRFIQStream::GainMode::SLOW;
    else if (gainModeStr == "default") gm = BladeRFIQStream::GainMode::DEFAULT_;
    else gm = BladeRFIQStream::GainMode::MANUAL;

    BladeRFIQStream::Config cfg;
    cfg.frequencyHz = freq;
    cfg.sampleRateHz = rate;
    cfg.bandwidthHz = bw;
    cfg.channel = ch;
    cfg.gainMode = gm;
    cfg.manualGainDb = gainDb;

    BladeRFIQStream::BufferConfig bufCfg;
    bufCfg.buffer_size = IQ_STREAM_READ_SIZE;
    bufCfg.num_buffers = 16;
    bufCfg.num_transfers = 8;

    LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "creating BladeRF IQStream; "
      << "freq=" << cfg.frequencyHz << "Hz, rate=" << cfg.sampleRateHz
      << "Hz, bw=" << cfg.bandwidthHz << "Hz, ch=" << cfg.channel
      << ", gainMode=" << gainModeStr << (gm == BladeRFIQStream::GainMode::MANUAL ? (", gainDb=" + std::to_string(cfg.manualGainDb)) : ""));

    auto brf = std::make_unique<BladeRFIQStream>(cfg, bufCfg);
    if (!brf->open()) {
      LOG_ERROR(BLADERF_STREAM, "failed to open bladeRF: " << brf->lastError());
      return nullptr;
    }

    // Emit streamReady event for BladeRF stream
    auto info = brf->getResolvedInfo();
    v8::Local<v8::Object> payload = v8::Object::New(isolate);
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "type").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "bladerf").ToLocalChecked()).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sourceName").ToLocalChecked(), v8::String::NewFromUtf8(isolate, "bladeRF").ToLocalChecked()).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "frequencyHz").ToLocalChecked(), v8::Number::New(isolate, info.frequencyHz)).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "sampleRateHz").ToLocalChecked(), v8::Number::New(isolate, info.sampleRateHz)).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "bandwidthHz").ToLocalChecked(), v8::Number::New(isolate, info.bandwidthHz)).Check();
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "channel").ToLocalChecked(), v8::Number::New(isolate, info.channel)).Check();
    const char* gainModeStr2 =
      (info.gainMode == BladeRFIQStream::GainMode::MANUAL ? "manual" :
       info.gainMode == BladeRFIQStream::GainMode::FAST   ? "fast"   :
       info.gainMode == BladeRFIQStream::GainMode::SLOW   ? "slow"   : "default");
    payload->Set(ctx, v8::String::NewFromUtf8(isolate, "gainMode").ToLocalChecked(), v8::String::NewFromUtf8(isolate, gainModeStr2).ToLocalChecked()).Check();
    if (info.gainMode == BladeRFIQStream::GainMode::MANUAL) {
      payload->Set(ctx, v8::String::NewFromUtf8(isolate, "manualGainDb").ToLocalChecked(), v8::Number::New(isolate, info.manualGainDb)).Check();
    }
    emitEventToJS("streamReady", payload);

    return brf;
  }

  LOG_ERROR(VIDEO_PROCESSOR_WRAPPER, "unknown stream.source: " << source);
  return nullptr;
}

} // namespace VideoDecodingAddon
